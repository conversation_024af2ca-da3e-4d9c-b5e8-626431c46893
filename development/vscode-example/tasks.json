{
  // See https://go.microsoft.com/fwlink/?LinkId=733558
  // for the documentation about the tasks.json format
  "version": "2.0.0",
  "tasks": [
    {
      "label": "Clean Honcho SocketIO Watch Schedule Worker",
      "detail": "When stopping the debug process from vscode window, the honcho won't receive the SIGINT signal. This task will send the SIGINT signal to the honcho processes.",
      "type": "shell",
      "command": "pkill -SIGINT -f bench; pkill -SIGINT -f socketio",
      "isBackground": false,
      "presentation": {
        "echo": true,
        "reveal": "silent",
        "focus": false,
        "panel": "shared",
        "showReuseMessage": false,
        "close": true
      }
    }
  ]
}
